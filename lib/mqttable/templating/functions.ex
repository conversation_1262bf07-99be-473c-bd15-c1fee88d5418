defmodule Mqttable.Templating.Functions do
  @moduledoc """
  Custom Liquid filters and functions for MQTT and IoT scenarios.

  This module provides a comprehensive set of custom functions that can be used
  in Liquid templates for generating realistic IoT data, device information,
  timestamps, and other MQTT-related content.

  Functions are organized into two main categories:
  - **Data Generation**: Functions that generate random data, including basic types,
    IoT/sensor data, and realistic fake data using the Faker library
  - **Data Processing**: Functions that process, transform, or manipulate data,
    including time/date operations, string operations, math operations, and encoding
  """

  @doc """
  Returns a list of all available custom function names.
  Used for template validation and autocomplete features.
  """
  def list_function_names do
    [
      # ============================================================================
      # DATA GENERATION FUNCTIONS
      # ============================================================================

      # Basic Random Data Generation (highest priority)
      "random_int",
      "random_float",
      "random_string",
      "random_choice",
      "random_bool",
      "random_hex",
      "uuid",

      # Person & Identity Data
      "name",
      "first_name",
      "last_name",
      "title",
      "prefix",
      "suffix",

      # Address & Location Data
      "address",
      "city",
      "country",
      "country_code",
      "state",
      "postcode",
      "latitude",
      "longitude",
      "timezone",
      "geohash",

      # Internet & Network Data
      "email",
      "username",
      "domain",
      "url",
      "ipv4",
      "ipv6",
      "mac_address",
      "ip_address",
      "user_agent",

      # Company & Business Data
      "company",
      "company_suffix",
      "buzzword",
      "catch_phrase",
      "department",

      # Text & Content Data
      "sentence",
      "paragraph",
      "word",
      "words",
      "markdown",
      "markdown_headers",
      "markdown_emphasis",
      "markdown_inline_code",
      "markdown_block_code",
      "markdown_ordered_list",
      "markdown_unordered_list",
      "markdown_table",

      # Commerce & Product Data
      "product_name",
      "price",
      "color_name",
      "rgb_hex",

      # File & Media Data
      "filename",
      "file_extension",
      "mime_type",
      "avatar_url",
      "image_url",

      # Food & Ingredients Data
      "dish",
      "ingredient",
      "spice",

      # Vehicle Data
      "vehicle",
      "vehicle_make",
      "vehicle_model",
      "vin",

      # Financial Data
      "currency_code",
      "currency_symbol",
      "iban",

      # Date Data
      "date_of_birth",
      "past_date",
      "future_date",

      # Blockchain Data
      "bitcoin_address",
      "ethereum_address",
      "ethereum_signature",

      # IoT & Sensor Data (specialized for IoT scenarios)
      "temperature",
      "humidity",
      "pressure",
      "battery_level",
      "signal_strength",
      "light_level",
      "air_quality",
      "device_status",
      "device_id",
      "firmware_version",
      "uptime",

      # ============================================================================
      # DATA PROCESSING FUNCTIONS
      # ============================================================================

      # Time & Date Processing
      "now",
      "timestamp",
      "iso8601",
      "unix_timestamp",
      "date_add",
      "date_format",
      "timezone_convert",

      # Data Encoding & Processing
      "base64_encode",
      "base64_decode",
      "json_encode",
      "json_decode",
      "hash",
      "url_encode",
      "url_decode",

      # String Processing
      "capitalize",
      "uppercase",
      "lowercase",
      "truncate",
      "pad_left",
      "pad_right",

      # Math Processing
      "round",
      "ceil",
      "floor",
      "abs",
      "min",
      "max",
      "clamp"
    ]
  end

  # ============================================================================
  # DATA GENERATION FUNCTIONS
  # ============================================================================

  # ----------------------------------------------------------------------------
  # BASIC RANDOM DATA GENERATION (Highest Priority)
  # ----------------------------------------------------------------------------

  @doc """
  Generates a random integer within the specified range.

  ## Parameters
  - min: minimum value (default: 0)
  - max: maximum value (default: 100)

  ## Usage
  - As variable: {{ random_int }}
  - As filter: {{ min | random_int: max }}
  """
  def random_int(min \\ 0, max \\ 100)

  def random_int(min, max) when is_number(min) and is_number(max) do
    Enum.random(min..max)
  end

  @doc """
  Generates a random float within the specified range.

  ## Parameters
  - min: minimum value (default: 0.0)
  - max: maximum value (default: 1.0)
  - precision: decimal places (default: 2)
  """
  def random_float(min \\ 0.0, max \\ 1.0, precision \\ 2) do
    (min + :rand.uniform() * (max - min))
    |> Float.round(precision)
  end

  @doc """
  Generates a random string of specified length.

  ## Parameters
  - length: string length (default: 8)
  - charset: character set - "alphanumeric", "alpha", "numeric", "hex" (default: "alphanumeric")
  """
  def random_string(length \\ 8, charset \\ "alphanumeric") do
    chars =
      case charset do
        "alpha" -> ?a..?z |> Enum.to_list()
        "numeric" -> ?0..?9 |> Enum.to_list()
        "hex" -> (?0..?9 |> Enum.to_list()) ++ (?a..?f |> Enum.to_list())
        _ -> (?a..?z |> Enum.to_list()) ++ (?0..?9 |> Enum.to_list())
      end

    1..length
    |> Enum.map(fn _ -> Enum.random(chars) end)
    |> List.to_string()
  end

  @doc """
  Returns a random choice from the provided array.

  ## Parameters
  - choices: list of choices
  """
  def random_choice(choices) when is_list(choices) do
    Enum.random(choices)
  end

  @doc """
  Generates a random boolean value.
  """
  def random_bool do
    Enum.random([true, false])
  end

  @doc """
  Generates a random hex string.

  ## Parameters
  - length: string length (default: 8)
  """
  def random_hex(length \\ 8) do
    random_string(length, "hex")
  end

  @doc """
  Generates a random UUID v4.
  """
  def uuid do
    Faker.UUID.v4()
  end

  # ----------------------------------------------------------------------------
  # PERSON & IDENTITY DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a full name using Faker.
  """
  def name do
    Faker.Person.name()
  end

  @doc """
  Generates a first name using Faker.
  """
  def first_name do
    Faker.Person.first_name()
  end

  @doc """
  Generates a last name using Faker.
  """
  def last_name do
    Faker.Person.last_name()
  end

  @doc """
  Generates a title using Faker.
  """
  def title do
    Faker.Person.title()
  end

  @doc """
  Generates a name prefix using Faker.
  """
  def prefix do
    Faker.Person.prefix()
  end

  @doc """
  Generates a name suffix using Faker.
  """
  def suffix do
    Faker.Person.suffix()
  end

  # ----------------------------------------------------------------------------
  # ADDRESS & LOCATION DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a street address using Faker.
  """
  def address do
    Faker.Address.street_address()
  end

  @doc """
  Generates a city name using Faker.
  """
  def city do
    Faker.Address.city()
  end

  @doc """
  Generates a country name using Faker.
  """
  def country do
    Faker.Address.country()
  end

  @doc """
  Generates a country code using Faker.
  """
  def country_code do
    Faker.Address.country_code()
  end

  @doc """
  Generates a state name using Faker.
  """
  def state do
    Faker.Address.state()
  end

  @doc """
  Generates a postcode using Faker.
  """
  def postcode do
    Faker.Address.postcode()
  end

  @doc """
  Generates a latitude using Faker.
  """
  def latitude do
    Faker.Address.latitude()
  end

  @doc """
  Generates a longitude using Faker.
  """
  def longitude do
    Faker.Address.longitude()
  end

  @doc """
  Generates a timezone using Faker.
  """
  def timezone do
    Faker.Address.time_zone()
  end

  @doc """
  Generates a geohash using Faker.
  """
  def geohash do
    Faker.Address.geohash()
  end

  # ----------------------------------------------------------------------------
  # INTERNET & NETWORK DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates an email address using Faker.
  """
  def email do
    Faker.Internet.email()
  end

  @doc """
  Generates a username using Faker.
  """
  def username do
    Faker.Internet.user_name()
  end

  @doc """
  Generates a domain name using Faker.
  """
  def domain do
    Faker.Internet.domain_name()
  end

  @doc """
  Generates a URL using Faker.
  """
  def url do
    Faker.Internet.url()
  end

  @doc """
  Generates an IPv4 address using Faker.
  """
  def ipv4 do
    Faker.Internet.ip_v4_address()
  end

  @doc """
  Generates an IPv6 address using Faker.
  """
  def ipv6 do
    Faker.Internet.ip_v6_address()
  end

  @doc """
  Generates a user agent string using Faker.
  """
  def user_agent do
    Faker.Internet.UserAgent.user_agent()
  end

  # Note: We keep both mac_address (custom IoT implementation) and ip_address (custom with type parameter)
  # These provide more flexibility than the basic Faker equivalents

  # ----------------------------------------------------------------------------
  # COMPANY & BUSINESS DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a company name using Faker.
  """
  def company do
    Faker.Company.name()
  end

  @doc """
  Generates a company suffix using Faker.
  """
  def company_suffix do
    Faker.Company.suffix()
  end

  @doc """
  Generates a business buzzword using Faker.
  """
  def buzzword do
    Faker.Company.buzzword()
  end

  @doc """
  Generates a company catch phrase using Faker.
  """
  def catch_phrase do
    Faker.Company.catch_phrase()
  end

  @doc """
  Generates a department name using Faker.
  """
  def department do
    Faker.Commerce.department()
  end

  # ----------------------------------------------------------------------------
  # TEXT & CONTENT DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a sentence using Faker.
  """
  def sentence do
    Faker.Lorem.sentence()
  end

  @doc """
  Generates a paragraph using Faker.
  """
  def paragraph do
    Faker.Lorem.paragraph()
  end

  @doc """
  Generates a word using Faker.
  """
  def word do
    Faker.Lorem.word()
  end

  @doc """
  Generates words using Faker.

  ## Parameters
  - count: number of words (default: 3)
  """
  def words(count \\ 3) do
    Faker.Lorem.words(count) |> Enum.join(" ")
  end

  @doc """
  Generates markdown content using Faker.
  """
  def markdown do
    Faker.Markdown.markdown()
  end

  @doc """
  Generates markdown headers using Faker.
  """
  def markdown_headers do
    Faker.Markdown.headers()
  end

  @doc """
  Generates markdown emphasis using Faker.
  """
  def markdown_emphasis do
    Faker.Markdown.emphasis()
  end

  @doc """
  Generates markdown inline code using Faker.
  """
  def markdown_inline_code do
    Faker.Markdown.inline_code()
  end

  @doc """
  Generates markdown block code using Faker.
  """
  def markdown_block_code do
    Faker.Markdown.block_code()
  end

  @doc """
  Generates markdown ordered list using Faker.
  """
  def markdown_ordered_list do
    Faker.Markdown.ordered_list()
  end

  @doc """
  Generates markdown unordered list using Faker.
  """
  def markdown_unordered_list do
    Faker.Markdown.unordered_list()
  end

  @doc """
  Generates markdown table using Faker.
  """
  def markdown_table do
    Faker.Markdown.table()
  end

  # ----------------------------------------------------------------------------
  # COMMERCE & PRODUCT DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a product name using Faker.
  """
  def product_name do
    Faker.Commerce.product_name()
  end

  @doc """
  Generates a price using Faker.
  """
  def price do
    Faker.Commerce.price()
  end

  @doc """
  Generates a color name using Faker.
  """
  def color_name do
    Faker.Color.name()
  end

  @doc """
  Generates a RGB hex color code using Faker.
  """
  def rgb_hex do
    Faker.Color.rgb_hex()
  end

  # ----------------------------------------------------------------------------
  # FILE & MEDIA DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a filename using Faker.
  """
  def filename do
    Faker.File.file_name()
  end

  @doc """
  Generates a file extension using Faker.
  """
  def file_extension do
    Faker.File.file_extension()
  end

  @doc """
  Generates a MIME type using Faker.
  """
  def mime_type do
    Faker.File.mime_type()
  end

  @doc """
  Generates an avatar URL.
  """
  def avatar_url(slug \\ "mqttable", width \\ "200", height \\ "200") do
    Faker.Avatar.image_url(slug, width, height)
  end

  @doc """
  Generates an image URL using Faker.
  """
  def image_url do
    Faker.Internet.image_url()
  end

  # ----------------------------------------------------------------------------
  # FOOD & INGREDIENTS DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a dish name using Faker.
  """
  def dish do
    Faker.Food.dish()
  end

  @doc """
  Generates an ingredient using Faker.
  """
  def ingredient do
    Faker.Food.ingredient()
  end

  @doc """
  Generates a spice using Faker.
  """
  def spice do
    Faker.Food.spice()
  end

  # ----------------------------------------------------------------------------
  # VEHICLE DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a vehicle make and model using Faker.
  """
  def vehicle do
    Faker.Vehicle.make_and_model()
  end

  @doc """
  Generates a vehicle make using Faker.
  """
  def vehicle_make do
    Faker.Vehicle.make()
  end

  @doc """
  Generates a vehicle model using Faker.
  """
  def vehicle_model do
    Faker.Vehicle.model()
  end

  @doc """
  Generates a VIN using Faker.
  """
  def vin do
    Faker.Vehicle.vin()
  end

  # ----------------------------------------------------------------------------
  # FINANCIAL DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a currency code using Faker.
  """
  def currency_code do
    Faker.Currency.code()
  end

  @doc """
  Generates a currency symbol using Faker.
  """
  def currency_symbol do
    Faker.Currency.symbol()
  end

  @doc """
  Generates an IBAN using Faker.
  """
  def iban do
    Faker.Code.iban()
  end

  # ----------------------------------------------------------------------------
  # DATE DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a date of birth using Faker.
  """
  def date_of_birth do
    Faker.Date.date_of_birth() |> Date.to_iso8601()
  end

  @doc """
  Generates a past date using Faker.

  ## Parameters
  - days: number of days in the past (default: 365)
  """
  def past_date(days \\ 365) do
    Faker.Date.backward(days) |> Date.to_iso8601()
  end

  @doc """
  Generates a future date using Faker.

  ## Parameters
  - days: number of days in the future (default: 365)
  """
  def future_date(days \\ 365) do
    Faker.Date.forward(days) |> Date.to_iso8601()
  end

  # ----------------------------------------------------------------------------
  # BLOCKCHAIN DATA
  # ----------------------------------------------------------------------------

  @doc """
  Generates a Bitcoin address using Faker.
  """
  def bitcoin_address do
    Faker.Blockchain.Bitcoin.address()
  end

  @doc """
  Generates an Ethereum address using Faker.
  """
  def ethereum_address do
    Faker.Blockchain.Ethereum.address()
  end

  @doc """
  Generates an Ethereum signature using Faker.
  """
  def ethereum_signature do
    Faker.Blockchain.Ethereum.signature()
  end

  # ----------------------------------------------------------------------------
  # IOT & SENSOR DATA (Specialized for IoT scenarios)
  # ----------------------------------------------------------------------------

  @doc """
  Generates a random temperature value within the specified range.

  ## Parameters
  - min: minimum temperature (default: 20)
  - max: maximum temperature (default: 25)
  - unit: temperature unit - "celsius", "fahrenheit", "kelvin" (default: "celsius")

  ## Examples
      {{ temperature }}             # Random between 20-25°C
      {{ "" | temperature: 15, 30 }}     # Random between 15-30°C (filter syntax)
  """
  def temperature(min \\ 20, max \\ 25, unit \\ "celsius")

  def temperature(min, max, unit) when is_number(min) and is_number(max) do
    base_temp = min + :rand.uniform() * (max - min)

    case unit do
      "fahrenheit" -> Float.round(base_temp * 9 / 5 + 32, 1)
      "kelvin" -> Float.round(base_temp + 273.15, 1)
      _ -> Float.round(base_temp, 1)
    end
  end

  @doc """
  Generates a random humidity percentage.

  ## Parameters
  - min: minimum humidity (default: 30)
  - max: maximum humidity (default: 80)
  """
  def humidity(min \\ 30, max \\ 80) do
    (min + :rand.uniform() * (max - min))
    |> Float.round(1)
  end

  @doc """
  Generates a random pressure value.

  ## Parameters
  - min: minimum pressure (default: 1000)
  - max: maximum pressure (default: 1030)
  - unit: pressure unit - "hpa", "psi", "mmhg" (default: "hpa")
  """
  def pressure(min \\ 1000, max \\ 1030, unit \\ "hpa") do
    base_pressure = min + :rand.uniform() * (max - min)

    case unit do
      "psi" -> Float.round(base_pressure * 0.0145038, 2)
      "mmhg" -> Float.round(base_pressure * 0.750062, 1)
      _ -> Float.round(base_pressure, 1)
    end
  end

  @doc """
  Generates a random battery level percentage.
  """
  def battery_level do
    Enum.random(2..100)
  end

  @doc """
  Generates a random signal strength value (RSSI).

  ## Parameters
  - min: minimum RSSI (default: -90)
  - max: maximum RSSI (default: -30)
  """
  def signal_strength(min \\ -90, max \\ -30) do
    Enum.random(min..max)
  end

  @doc """
  Generates a random light level value in lux.

  ## Parameters
  - min: minimum lux (default: 0)
  - max: maximum lux (default: 1000)
  """
  def light_level(min \\ 0, max \\ 1000) do
    Enum.random(min..max)
  end

  @doc """
  Generates a random air quality index.

  ## Parameters
  - min: minimum AQI (default: 0)
  - max: maximum AQI (default: 300)
  """
  def air_quality(min \\ 0, max \\ 300) do
    Enum.random(min..max)
  end

  # Device Functions (IoT-specific, preserved as custom implementations)

  @doc """
  Generates a random device status.
  """
  def device_status do
    Enum.random(["online", "offline", "maintenance", "error", "idle", "active"])
  end

  @doc """
  Generates a device ID with optional prefix.

  ## Parameters
  - prefix: device ID prefix (default: "device")
  """
  def device_id(prefix \\ "device", length \\ 8) do
    suffix = :crypto.strong_rand_bytes(length) |> Base.encode16(case: :lower)
    "#{prefix}_#{suffix}"
  end

  @doc """
  Generates a random firmware version string.
  """
  def firmware_version do
    major = Enum.random(1..5)
    minor = Enum.random(0..9)
    patch = Enum.random(0..20)
    "#{major}.#{minor}.#{patch}"
  end

  @doc """
  Generates a random uptime value in seconds.

  ## Parameters
  - max_days: maximum uptime in days (default: 30)
  """
  def uptime(max_days \\ 30) do
    max_seconds = max_days * 24 * 60 * 60
    Enum.random(0..max_seconds)
  end

  @doc """
  Generates a random MAC address.
  """
  def mac_address do
    Faker.Internet.mac_address()
  end

  @doc """
  Generates a random IP address.

  ## Parameters
  - type: "ipv4" or "ipv6" (default: "ipv4")
  """
  def ip_address(type \\ "ipv4") do
    case type do
      "ipv6" ->
        Faker.Internet.ip_v6_address()

      _ ->
        Faker.Internet.ip_v4_address()
    end
  end

  # ============================================================================
  # DATA PROCESSING FUNCTIONS
  # ============================================================================

  # ----------------------------------------------------------------------------
  # TIME & DATE PROCESSING
  # ----------------------------------------------------------------------------

  @doc """
  Returns the current timestamp in the specified format.

  ## Parameters
  - format: timestamp format - "iso8601", "unix", "rfc3339" (default: "iso8601")
  - timezone: timezone (default: "UTC")
  """
  def now(format \\ "iso8601", _timezone \\ "UTC") do
    datetime = DateTime.utc_now()

    case format do
      "unix" -> DateTime.to_unix(datetime)
      "rfc3339" -> DateTime.to_iso8601(datetime)
      _ -> DateTime.to_iso8601(datetime)
    end
  end

  @doc """
  Returns a timestamp with the specified offset.

  ## Parameters
  - offset: offset in seconds (can be negative)
  - format: timestamp format (default: "iso8601")
  """
  def timestamp(offset \\ 0, format \\ "iso8601") do
    datetime = DateTime.utc_now() |> DateTime.add(offset, :second)

    case format do
      "unix" -> DateTime.to_unix(datetime)
      "rfc3339" -> DateTime.to_iso8601(datetime)
      _ -> DateTime.to_iso8601(datetime)
    end
  end

  @doc """
  Returns the current timestamp in ISO8601 format.
  """
  def iso8601 do
    DateTime.utc_now() |> DateTime.to_iso8601()
  end

  @doc """
  Returns the current Unix timestamp.
  """
  def unix_timestamp do
    DateTime.utc_now() |> DateTime.to_unix()
  end

  @doc """
  Adds a time offset to the current datetime.

  ## Parameters
  - offset: offset in seconds (can be negative)
  - format: output format - "iso8601", "unix", "rfc3339" (default: "iso8601")

  ## Examples
      {{ 3600 | date_add }}           # Add 1 hour
      {{ -1800 | date_add: "unix" }}  # Subtract 30 minutes, return unix timestamp
  """
  def date_add(offset, format \\ "iso8601") when is_integer(offset) do
    datetime = DateTime.utc_now() |> DateTime.add(offset, :second)

    case format do
      "unix" -> DateTime.to_unix(datetime)
      "rfc3339" -> DateTime.to_iso8601(datetime)
      _ -> DateTime.to_iso8601(datetime)
    end
  end

  @doc """
  Formats a datetime according to the specified format string.

  ## Parameters
  - datetime: DateTime struct or ISO8601 string (default: current time)
  - format_string: strftime-style format string (default: "%Y-%m-%d %H:%M:%S")

  ## Examples
      {{ date_format }}                           # Current time with default format
      {{ "2023-01-01T12:00:00Z" | date_format: "%Y-%m-%d" }}  # Custom format
  """
  def date_format(datetime \\ nil, format_string \\ "%Y-%m-%d %H:%M:%S")

  def date_format(nil, format_string) do
    DateTime.utc_now() |> Calendar.strftime(format_string)
  end

  def date_format(datetime, format_string) when is_binary(datetime) do
    case DateTime.from_iso8601(datetime) do
      {:ok, dt, _} -> Calendar.strftime(dt, format_string)
      {:error, _} -> datetime
    end
  end

  def date_format(%DateTime{} = datetime, format_string) do
    Calendar.strftime(datetime, format_string)
  end

  @doc """
  Converts a datetime from one timezone to another.

  ## Parameters
  - datetime: DateTime struct or ISO8601 string (default: current time)
  - target_timezone: target timezone (default: "UTC")

  ## Examples
      {{ timezone_convert }}                      # Current time to UTC
      {{ "2023-01-01T12:00:00Z" | timezone_convert: "America/New_York" }}
  """
  def timezone_convert(datetime \\ nil, target_timezone \\ "UTC")

  def timezone_convert(nil, target_timezone) do
    case convert_timezone(DateTime.utc_now(), target_timezone) do
      {:ok, converted} -> DateTime.to_iso8601(converted)
      {:error, _} -> DateTime.utc_now() |> DateTime.to_iso8601()
    end
  end

  def timezone_convert(datetime, target_timezone) when is_binary(datetime) do
    case DateTime.from_iso8601(datetime) do
      {:ok, dt, _} ->
        case convert_timezone(dt, target_timezone) do
          {:ok, converted} -> DateTime.to_iso8601(converted)
          {:error, _} -> datetime
        end

      {:error, _} ->
        datetime
    end
  end

  def timezone_convert(%DateTime{} = datetime, target_timezone) do
    case convert_timezone(datetime, target_timezone) do
      {:ok, converted} -> DateTime.to_iso8601(converted)
      {:error, _} -> DateTime.to_iso8601(datetime)
    end
  end

  # Helper function for timezone conversion
  defp convert_timezone(datetime, "UTC"), do: {:ok, DateTime.shift_zone!(datetime, "Etc/UTC")}

  defp convert_timezone(datetime, timezone) do
    try do
      {:ok, DateTime.shift_zone!(datetime, timezone)}
    rescue
      _ -> {:error, :invalid_timezone}
    end
  end

  # ----------------------------------------------------------------------------
  # DATA ENCODING & PROCESSING
  # ----------------------------------------------------------------------------

  @doc """
  Base64 encodes the input string.
  """
  def base64_encode(data) when is_binary(data) do
    Base.encode64(data)
  end

  @doc """
  Base64 decodes the input string.
  """
  def base64_decode(data) when is_binary(data) do
    case Base.decode64(data) do
      {:ok, decoded} -> decoded
      :error -> ""
    end
  end

  @doc """
  JSON encodes the input data.
  """
  def json_encode(data) do
    case Jason.encode(data) do
      {:ok, json} -> json
      {:error, _} -> "{}"
    end
  end

  @doc """
  JSON decodes the input string.

  ## Parameters
  - json_string: JSON string to decode

  ## Returns
  - Decoded data structure on success
  - Empty map {} on error

  ## Examples
      {{ '{"name": "John", "age": 30}' | json_decode }}
  """
  def json_decode(json_string) when is_binary(json_string) do
    case Jason.decode(json_string) do
      {:ok, data} -> data
      {:error, _} -> %{}
    end
  end

  @doc """
  Generates a hash of the input data.

  ## Parameters
  - data: data to hash
  - algorithm: hash algorithm - "md5", "sha1", "sha256" (default: "sha256")
  """
  def hash(data, algorithm \\ "sha256") when is_binary(data) do
    algo =
      case algorithm do
        "md5" -> :md5
        "sha1" -> :sha
        _ -> :sha256
      end

    :crypto.hash(algo, data) |> Base.encode16(case: :lower)
  end

  @doc """
  URL encodes the input string.
  """
  def url_encode(data) when is_binary(data) do
    URI.encode(data)
  end

  @doc """
  URL decodes the input string.
  """
  def url_decode(data) when is_binary(data) do
    URI.decode(data)
  end

  # ----------------------------------------------------------------------------
  # MATH PROCESSING
  # ----------------------------------------------------------------------------

  @doc """
  Rounds a number to the specified precision.
  """
  def round(number, precision \\ 0) when is_number(number) do
    Float.round(number * 1.0, precision)
  end

  @doc """
  Returns the minimum value from a list of numbers or two numbers.

  ## Parameters
  - numbers: list of numbers or first number
  - second: second number (when first parameter is a number)

  ## Examples
      {{ [1, 2, 3, 4, 5] | min }}    # Returns 1
      {{ 5 | min: 3 }}               # Returns 3
  """
  def min(numbers) when is_list(numbers) do
    case Enum.filter(numbers, &is_number/1) do
      [] -> 0
      filtered_numbers -> Enum.min(filtered_numbers)
    end
  end

  def min(a, b) when is_number(a) and is_number(b) do
    Kernel.min(a, b)
  end

  @doc """
  Returns the maximum value from a list of numbers or two numbers.

  ## Parameters
  - numbers: list of numbers or first number
  - second: second number (when first parameter is a number)

  ## Examples
      {{ [1, 2, 3, 4, 5] | max }}    # Returns 5
      {{ 5 | max: 3 }}               # Returns 5
  """
  def max(numbers) when is_list(numbers) do
    case Enum.filter(numbers, &is_number/1) do
      [] -> 0
      filtered_numbers -> Enum.max(filtered_numbers)
    end
  end

  def max(a, b) when is_number(a) and is_number(b) do
    Kernel.max(a, b)
  end

  @doc """
  Clamps a number between min and max values.
  """
  def clamp(value, min_val, max_val)
      when is_number(value) and is_number(min_val) and is_number(max_val) do
    value |> Kernel.max(min_val) |> Kernel.min(max_val)
  end

  @doc """
  Returns the ceiling of a number.
  """
  def ceil(number) when is_number(number) do
    Float.ceil(number * 1.0)
  end

  @doc """
  Returns the floor of a number.
  """
  def floor(number) when is_number(number) do
    Float.floor(number * 1.0)
  end

  @doc """
  Returns the absolute value of a number.
  """
  def abs(number) when is_number(number) do
    Kernel.abs(number)
  end

  # ----------------------------------------------------------------------------
  # STRING PROCESSING
  # ----------------------------------------------------------------------------

  @doc """
  Capitalizes the first letter of a string.
  """
  def capitalize(string) when is_binary(string) do
    String.capitalize(string)
  end

  @doc """
  Converts a string to uppercase.
  """
  def uppercase(string) when is_binary(string) do
    String.upcase(string)
  end

  @doc """
  Converts a string to lowercase.
  """
  def lowercase(string) when is_binary(string) do
    String.downcase(string)
  end

  @doc """
  Truncates a string to the specified length.

  ## Parameters
  - string: the string to truncate
  - length: maximum length (default: 50)
  - suffix: suffix to add when truncated (default: "...")
  """
  def truncate(string, length \\ 50, suffix \\ "...") when is_binary(string) do
    if String.length(string) <= length do
      string
    else
      String.slice(string, 0, length - String.length(suffix)) <> suffix
    end
  end

  @doc """
  Pads a string on the left with the specified character.

  ## Parameters
  - string: the string to pad
  - length: target length
  - pad_char: character to pad with (default: " ")
  """
  def pad_left(string, length, pad_char \\ " ") when is_binary(string) do
    String.pad_leading(string, length, pad_char)
  end

  @doc """
  Pads a string on the right with the specified character.

  ## Parameters
  - string: the string to pad
  - length: target length
  - pad_char: character to pad with (default: " ")
  """
  def pad_right(string, length, pad_char \\ " ") when is_binary(string) do
    String.pad_trailing(string, length, pad_char)
  end
end
